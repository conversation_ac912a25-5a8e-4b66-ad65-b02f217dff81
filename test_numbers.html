<!DOCTYPE html>
<html>
<head>
    <title>Test English Numbers</title>
</head>
<body>
    <h1>Testing English Number Formatting</h1>
    
    <!-- Test numbers -->
    <p>Test 1: 1234.5 → <span id="test1"></span></p>
    <p>Test 2: 12345.67 → <span id="test2"></span></p>
    <p>Test 3: 123456.789 → <span id="test3"></span></p>
    <p>Test 4: 1000000 → <span id="test4"></span></p>
    
    <script>
        // تنسيق المبالغ المالية بالتنسيق الإنجليزي
        function formatCurrency(amount, currencyCode = null) {
            try {
                // تحويل إلى رقم
                const num = parseFloat(amount);
                if (isNaN(num)) {
                    return `${amount} ₪`;
                }
                
                // تنسيق الرقم بالتنسيق الإنجليزي مع فاصلة الآلاف ورقم عشري واحد
                const formatted = new Intl.NumberFormat('en-US', {
                    minimumFractionDigits: 1,
                    maximumFractionDigits: 1
                }).format(num);
                
                return `${formatted} ₪`;
            } catch (error) {
                // fallback إذا فشل التنسيق
                return `${amount} ₪`;
            }
        }

        // تنسيق الأرقام الصحيحة بالتنسيق الإنجليزي
        function formatNumber(number) {
            try {
                const num = parseInt(number);
                if (isNaN(num)) {
                    return number;
                }
                
                // تنسيق الرقم بالتنسيق الإنجليزي مع فاصلة الآلاف
                return new Intl.NumberFormat('en-US').format(num);
            } catch (error) {
                return number;
            }
        }
        
        // Test the functions
        document.getElementById('test1').textContent = formatCurrency(1234.5);
        document.getElementById('test2').textContent = formatCurrency(12345.67);
        document.getElementById('test3').textContent = formatCurrency(123456.789);
        document.getElementById('test4').textContent = formatNumber(1000000);
    </script>
</body>
</html>
