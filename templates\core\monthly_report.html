{% extends 'base.html' %}
{% load static %}

{% block title %}التقرير الشهري{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="page-title mb-4">
            <i class="fas fa-chart-bar me-3"></i>
            التقرير الشهري
        </h1>
        <!-- نموذج الفلترة -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-filter me-2"></i>
                    فلترة التقرير
                </h5>
            </div>
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label for="{{ filter_form.start_date.id_for_label }}" class="form-label">{{ filter_form.start_date.label }}</label>
                        {{ filter_form.start_date }}
                    </div>
                    <div class="col-md-3">
                        <label for="{{ filter_form.end_date.id_for_label }}" class="form-label">{{ filter_form.end_date.label }}</label>
                        {{ filter_form.end_date }}
                    </div>
                    <div class="col-md-3">
                        <label for="{{ filter_form.member.id_for_label }}" class="form-label">{{ filter_form.member.label }}</label>
                        {{ filter_form.member }}
                    </div>
                    <div class="col-md-3">
                        <label for="{{ filter_form.payment_type.id_for_label }}" class="form-label">{{ filter_form.payment_type.label }}</label>
                        {{ filter_form.payment_type }}
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>عرض التقرير
                        </button>
                        <a href="{% url 'monthly_report_export_excel' %}?{{ request.GET.urlencode }}" class="btn btn-success">
                            <i class="fas fa-file-excel me-2"></i>تصدير Excel
                        </a>
                        <a href="{% url 'monthly_report_export_pdf' %}?{{ request.GET.urlencode }}" class="btn btn-danger">
                            <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                        </a>
                        <a href="{% url 'reports_dashboard' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- إحصائيات التقرير -->
        {% if summary_stats %}
        <div class="row mb-4">
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h5>{{ summary_stats.total_transactions }}</h5>
                        <small>إجمالي العمليات</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h5>{{ summary_stats.payment_count }}</h5>
                        <small>عدد الدفعات</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <h5>{{ summary_stats.expense_count }}</h5>
                        <small>عدد المصاريف</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h6>{{ summary_stats.total_payments|floatformat:2 }} ₪</h6>
                        <small>إجمالي الدفعات</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h6>{{ summary_stats.total_expenses|floatformat:2 }} ₪</h6>
                        <small>إجمالي المصاريف</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card {% if summary_stats.net_amount >= 0 %}bg-success{% else %}bg-danger{% endif %} text-white">
                    <div class="card-body text-center">
                        <h6>{{ summary_stats.net_amount|floatformat:2 }} ₪</h6>
                        <small>صافي المبلغ</small>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="card">
            <div class="card-body">
                {% if report_data %}
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-dark">
                            <tr>
                                {% for col in report_data.0.keys %}
                                <th>{{ col }}</th>
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for row in report_data %}
                            <tr class="{% if row.النوع == 'دفعة' %}table-success{% else %}table-danger{% endif %}">
                                {% for key, val in row.items %}
                                <td>
                                    {% if key == 'النوع' %}
                                        <span class="badge {% if val == 'دفعة' %}bg-success{% else %}bg-danger{% endif %}">
                                            {{ val }}
                                        </span>
                                    {% elif key == 'المبلغ' %}
                                        <strong>{{ val|floatformat:2 }} ₪</strong>
                                    {% elif key == 'التاريخ' %}
                                        {{ val|date:"Y-m-d" }}
                                    {% else %}
                                        {% if val|stringformat:'s'|floatformat != 'nan' and val|stringformat:'s'|floatformat != 'None' %}
                                            {{ val|add:0 }}
                                        {% else %}
                                            {{ val|default:"-" }}
                                        {% endif %}
                                    {% endif %}
                                </td>
                                {% endfor %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">لا توجد بيانات لهذا التقرير.</div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
