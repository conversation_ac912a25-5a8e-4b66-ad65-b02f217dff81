{% extends 'base.html' %}
{% load static %}

{% block title %}التقرير الشهري{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="page-title mb-4">
            <i class="fas fa-chart-bar me-3"></i>
            التقرير الشهري
        </h1>
        <form method="get" class="row g-3 mb-4">
            {{ filter_form.as_p }}
            <div class="col-12">
                <button type="submit" class="btn btn-primary">عرض التقرير</button>
                <a href="{% url 'monthly_report_export_excel' %}?{{ request.GET.urlencode }}" class="btn btn-success">تصدير Excel</a>
                <a href="{% url 'monthly_report_export_pdf' %}?{{ request.GET.urlencode }}" class="btn btn-danger">تصدير PDF</a>
            </div>
        </form>
        <div class="card">
            <div class="card-body">
                {% if report_data %}
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                {% for col in report_data.0.keys %}
                                <th>{{ col }}</th>
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for row in report_data %}
                            <tr>
                                {% for val in row.values %}
                                <td>{% if val|stringformat:'s'|floatformat != 'nan' and val|stringformat:'s'|floatformat != 'None' %}{{ val|add:0 }}{% else %}{{ val }}{% endif %}</td>
                                {% endfor %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">لا توجد بيانات لهذا التقرير.</div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
