from django.urls import path
from . import views

urlpatterns = [
    # Authentication
    path('', views.login_view, name='login'),
    path('login/', views.login_view, name='login'),
    path('login-help/', views.login_help_view, name='login_help'),
    path('logout/', views.logout_view, name='logout'),

    # Dashboard
    path('dashboard/', views.dashboard_view, name='dashboard'),

    # Organizations (Super Admin only)
    path('organizations/', views.organization_list_view, name='organization_list'),
    path('organizations/create/', views.organization_create_view, name='organization_create'),

    # Managers (Super Admin only)
    path('managers/create/', views.manager_create_view, name='manager_create'),

    # Members
    path('members/', views.member_list_view, name='member_list'),
    path('members/create/', views.member_create_view, name='member_create'),
    path('members/delete/<int:member_id>/', views.member_delete_view, name='member_delete'),

    # Payments
    path('payments/', views.payment_list_view, name='payment_list'),
    path('payments/create/', views.payment_create_view, name='payment_create'),

    # Expenses
    path('expenses/', views.expense_list_view, name='expense_list'),
    path('expenses/create/', views.expense_create_view, name='expense_create'),

    # Firebase Dashboard
    path('firebase-dashboard/', views.firebase_dashboard_view, name='firebase_dashboard'),

    # API endpoints for AJAX
    path('api/stats/', views.api_stats_view, name='api_stats'),
    path('api/monthly-payments/', views.api_monthly_payments_view, name='api_monthly_payments'),
    path('api/payment-types/', views.api_payment_types_view, name='api_payment_types'),

    # Reports
    path('reports/', views.reports_dashboard, name='reports_dashboard'),
    path('reports/monthly/', views.monthly_report_view, name='monthly_report'),
    path('reports/monthly/export/excel/', views.monthly_report_export_excel, name='monthly_report_export_excel'),
    path('reports/monthly/export/pdf/', views.monthly_report_export_pdf, name='monthly_report_export_pdf'),
    path('reports/member-status/', views.member_status_report, name='member_status_report'),
    path('reports/member-status/export/excel/', views.member_status_export_excel, name='member_status_export_excel'),
    path('reports/member-status/export/pdf/', views.member_status_export_pdf, name='member_status_export_pdf'),

    # Test view
    path('test-numbers/', views.test_numbers_view, name='test_numbers'),
]
