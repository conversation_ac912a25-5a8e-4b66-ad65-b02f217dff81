// تأكيد الحذف
function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
    return confirm(message);
}

// تأكيد العمليات المهمة
function confirmAction(message = 'هل أنت متأكد من هذا الإجراء؟') {
    return confirm(message);
}

// إظهار رسالة نجاح
function showSuccessMessage(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // إخفاء الرسالة بعد 5 ثوان
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// إظهار رسالة خطأ
function showErrorMessage(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // إخفاء الرسالة بعد 5 ثوان
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// تنسيق الأرقام العربية
function formatArabicNumber(number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().replace(/[0-9]/g, (w) => arabicNumbers[+w]);
}

// إعدادات العملة
const CURRENCY_SETTINGS = {
    default: 'ILS',
    currencies: {
        'ILS': {
            code: 'ILS',
            symbol: '₪',
            name: 'شيقل',
            locale: 'ar-PS'
        },
        'USD': {
            code: 'USD',
            symbol: '$',
            name: 'دولار',
            locale: 'en-US'
        },
        'EUR': {
            code: 'EUR',
            symbol: '€',
            name: 'يورو',
            locale: 'en-EU'
        },
        'SAR': {
            code: 'SAR',
            symbol: 'ر.س',
            name: 'ريال',
            locale: 'ar-SA'
        },
        'JOD': {
            code: 'JOD',
            symbol: 'د.أ',
            name: 'دينار',
            locale: 'ar-JO'
        }
    }
};

// تنسيق المبالغ المالية
function formatCurrency(amount, currencyCode = null) {
    const currency = currencyCode || CURRENCY_SETTINGS.default;
    const currencyInfo = CURRENCY_SETTINGS.currencies[currency];

    if (!currencyInfo) {
        return `${amount} شيقل`;
    }

    try {
        return new Intl.NumberFormat(currencyInfo.locale, {
            style: 'currency',
            currency: currencyInfo.code
        }).format(amount);
    } catch (error) {
        // fallback إذا فشل التنسيق
        return `${amount} ${currencyInfo.name}`;
    }
}

// تنسيق المبلغ مع اسم العملة
function formatCurrencyWithName(amount, currencyCode = null) {
    const currency = currencyCode || CURRENCY_SETTINGS.default;
    const currencyInfo = CURRENCY_SETTINGS.currencies[currency];

    if (!currencyInfo) {
        return `${amount} شيقل`;
    }

    return `${amount} ${currencyInfo.name}`;
}

// البحث في الجداول
function searchTable(inputId, tableId) {
    const input = document.getElementById(inputId);
    const table = document.getElementById(tableId);
    const rows = table.getElementsByTagName('tr');
    
    input.addEventListener('keyup', function() {
        const filter = this.value.toLowerCase();
        
        for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            const cells = row.getElementsByTagName('td');
            let found = false;
            
            for (let j = 0; j < cells.length; j++) {
                const cell = cells[j];
                if (cell.textContent.toLowerCase().indexOf(filter) > -1) {
                    found = true;
                    break;
                }
            }
            
            row.style.display = found ? '' : 'none';
        }
    });
}

// تحديث الإحصائيات في الوقت الفعلي
function updateStats() {
    fetch('/api/stats/')
        .then(response => response.json())
        .then(data => {
            // تحديث عدد الأعضاء
            const membersCount = document.getElementById('members-count');
            if (membersCount) {
                membersCount.textContent = data.members_count;
            }
            
            // تحديث إجمالي الدفعات
            const totalPayments = document.getElementById('total-payments');
            if (totalPayments) {
                totalPayments.textContent = formatCurrency(data.total_payments);
            }
            
            // تحديث إجمالي المصاريف
            const totalExpenses = document.getElementById('total-expenses');
            if (totalExpenses) {
                totalExpenses.textContent = formatCurrency(data.total_expenses);
            }
            
            // تحديث الرصيد
            const balance = document.getElementById('balance');
            if (balance) {
                balance.textContent = formatCurrency(data.balance);
            }
        })
        .catch(error => {
            console.error('خطأ في تحديث الإحصائيات:', error);
        });
}

// رسم الرسوم البيانية
function createChart(canvasId, type, data, options = {}) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) return;
    
    return new Chart(ctx, {
        type: type,
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                },
                x: {
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            },
            ...options
        }
    });
}

// تحميل البيانات وإنشاء الرسوم البيانية
function loadCharts() {
    // رسم بياني للدفعات الشهرية
    fetch('/api/monthly-payments/')
        .then(response => response.json())
        .then(data => {
            const chartData = {
                labels: data.labels,
                datasets: [{
                    label: 'الدفعات الشهرية',
                    data: data.values,
                    backgroundColor: 'rgba(102, 126, 234, 0.2)',
                    borderColor: 'rgba(102, 126, 234, 1)',
                    borderWidth: 2,
                    fill: true
                }]
            };
            
            createChart('monthly-payments-chart', 'line', chartData);
        });
    
    // رسم بياني لأنواع الدفعات
    fetch('/api/payment-types/')
        .then(response => response.json())
        .then(data => {
            const chartData = {
                labels: data.labels,
                datasets: [{
                    data: data.values,
                    backgroundColor: [
                        '#667eea',
                        '#764ba2',
                        '#f093fb',
                        '#f5576c',
                        '#4facfe'
                    ]
                }]
            };
            
            createChart('payment-types-chart', 'doughnut', chartData);
        });
}

// التحقق من صحة النماذج
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return true;
    
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    // التحقق من صحة البريد الإلكتروني
    const emailFields = form.querySelectorAll('input[type="email"]');
    emailFields.forEach(field => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (field.value && !emailRegex.test(field.value)) {
            field.classList.add('is-invalid');
            isValid = false;
        }
    });
    
    // التحقق من صحة أرقام الهاتف
    const phoneFields = form.querySelectorAll('input[type="tel"], input[name*="phone"]');
    phoneFields.forEach(field => {
        const phoneRegex = /^[0-9+\-\s()]+$/;
        if (field.value && !phoneRegex.test(field.value)) {
            field.classList.add('is-invalid');
            isValid = false;
        }
    });
    
    return isValid;
}

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحديث الإحصائيات كل 30 ثانية
    updateStats();
    setInterval(updateStats, 30000);
    
    // تحميل الرسوم البيانية
    loadCharts();
    
    // إعداد البحث في الجداول
    const searchInputs = document.querySelectorAll('[data-search-table]');
    searchInputs.forEach(input => {
        const tableId = input.getAttribute('data-search-table');
        searchTable(input.id, tableId);
    });
    
    // إعداد تأكيد الحذف
    const deleteButtons = document.querySelectorAll('[data-confirm-delete]');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const message = this.getAttribute('data-confirm-delete') || 'هل أنت متأكد من الحذف؟';
            if (!confirmDelete(message)) {
                e.preventDefault();
            }
        });
    });
    
    // إعداد التحقق من النماذج
    const forms = document.querySelectorAll('form[data-validate]');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this.id)) {
                e.preventDefault();
                showErrorMessage('يرجى تعبئة جميع الحقول المطلوبة بشكل صحيح');
            }
        });
    });
    
    // إعداد تنسيق حقول المبالغ المالية
    const amountFields = document.querySelectorAll('input[name*="amount"]');
    amountFields.forEach(field => {
        field.addEventListener('input', function() {
            // إزالة الأحرف غير الرقمية والنقطة العشرية
            this.value = this.value.replace(/[^0-9.]/g, '');
            
            // التأكد من وجود نقطة عشرية واحدة فقط
            const parts = this.value.split('.');
            if (parts.length > 2) {
                this.value = parts[0] + '.' + parts.slice(1).join('');
            }
        });
    });
});

// تصدير البيانات
function exportData(url, filename) {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// طباعة التقارير
function printReport() {
    window.print();
}
