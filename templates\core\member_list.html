{% extends 'base.html' %}
{% load static %}
{% load currency_tags %}

{% block title %}قائمة الأعضاء - نظام إدارة التضامن{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="page-title">
                <i class="fas fa-users me-3"></i>
                قائمة الأعضاء
                {% if organization %}
                    <small class="text-muted">({{ current_members_count }}/{{ max_members_limit }})</small>
                {% endif %}
            </h1>
            {% if can_add_member %}
                <a href="{% url 'member_create' %}" class="btn btn-primary">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة عضو جديد
                </a>
            {% else %}
                <button class="btn btn-secondary" disabled title="لقد وصلت الحد الأقصى لعدد الأعضاء">
                    <i class="fas fa-ban me-2"></i>
                    الحد الأقصى مكتمل
                </button>
            {% endif %}
        </div>
    </div>
</div>

<!-- معلومات الحد الأقصى للأعضاء -->
{% if organization %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-info">
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="stat-item">
                            <h4 class="text-primary">{{ current_members_count }}</h4>
                            <small class="text-muted">الأعضاء الحاليين</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <h4 class="text-success">{{ max_members_limit }}</h4>
                            <small class="text-muted">الحد الأقصى</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <h4 class="{% if remaining_slots > 0 %}text-warning{% else %}text-danger{% endif %}">{{ remaining_slots }}</h4>
                            <small class="text-muted">الأماكن المتبقية</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            {% if can_add_member %}
                                <span class="badge bg-success fs-6">متاح</span>
                            {% else %}
                                <span class="badge bg-danger fs-6">مكتمل</span>
                            {% endif %}
                            <br><small class="text-muted">حالة الإضافة</small>
                        </div>
                    </div>
                </div>

                {% if remaining_slots <= 3 and remaining_slots > 0 %}
                <div class="alert alert-warning mt-3 mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تنبيه:</strong> يتبقى {{ remaining_slots }} أماكن فقط لإضافة أعضاء جدد.
                </div>
                {% elif remaining_slots == 0 %}
                <div class="alert alert-danger mt-3 mb-0">
                    <i class="fas fa-ban me-2"></i>
                    <strong>تحذير:</strong> لقد وصلت الحد الأقصى لعدد الأعضاء. للحصول على المزيد من الأماكن، يرجى التواصل مع مالك المشروع على الرقم <strong>0598455262</strong>.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- فلاتر البحث -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <input type="text" class="form-control" name="search" 
                               value="{{ search }}" placeholder="البحث بالاسم أو الهاتف أو المدينة">
                    </div>
                    <div class="col-md-3">
                        <select name="status" class="form-control">
                            <option value="">جميع الحالات</option>
                            <option value="active" {% if status == 'active' %}selected{% endif %}>نشط</option>
                            <option value="inactive" {% if status == 'inactive' %}selected{% endif %}>غير نشط</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search me-2"></i>
                            بحث
                        </button>
                        <a href="{% url 'member_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    الأعضاء المسجلين ({{ members.count }})
                </h5>
            </div>
            <div class="card-body">
                {% if members %}
                <div class="table-responsive">
                    <table class="table table-hover" id="membersTable">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>الهاتف</th>
                                <th>المدينة</th>
                                <th>آخر دفعة</th>
                                <th>إجمالي الدفعات</th>
                                <th>الحالة</th>
                                <th>تاريخ التسجيل</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for member in members %}
                            <tr>
                                <td>
                                    <strong>{{ member.name }}</strong>
                                    {% if member.notes %}
                                    <br><small class="text-muted">{{ member.notes|truncatechars:30 }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="tel:{{ member.phone }}">{{ member.phone }}</a>
                                </td>
                                <td>{{ member.city }}</td>
                                <td>
                                    {% with last_payment=member.get_last_payment %}
                                        {% if last_payment %}
                                            {{ last_payment.payment_date|date:"Y/m/d" }}
                                            <br><small class="text-muted">{{ last_payment.amount|english_currency:0 }}</small>
                                        {% else %}
                                            <span class="text-muted">لا توجد دفعات</span>
                                        {% endif %}
                                    {% endwith %}
                                </td>
                                <td>
                                    <span class="fw-bold text-success">{{ member.get_total_payments|english_currency:0 }}</span>
                                </td>
                                <td>
                                    {% if member.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-danger">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>{{ member.created_at|date:"Y/m/d" }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{# TODO: member detail url #}" class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{# TODO: member edit url #}" class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'payment_create' %}?member_id={{ member.id }}" class="btn btn-sm btn-outline-success" title="إضافة دفعة">
                                            <i class="fas fa-money-bill"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger btn-delete-member" 
                                                title="حذف" 
                                                data-member-id="{{ member.id }}"
                                                data-member-name="{{ member.name }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد أعضاء مسجلين</h4>
                    <p class="text-muted">ابدأ بإضافة عضو جديد للمؤسسة</p>
                    <a href="{% url 'member_create' %}" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة عضو جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
{% if members %}
<div class="row mt-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h4>{{ members.count|english_number:0 }}</h4>
                <p class="mb-0">إجمالي الأعضاء</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <h4>{{ members|length|english_number:0 }}</h4>
                <p class="mb-0">الأعضاء النشطون</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <h4>0</h4>
                <p class="mb-0">الأعضاء المتأخرون</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                <h4>{{ total_payments|default:0|english_currency }}</h4>
                <p class="mb-0">إجمالي الدفعات</p>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    // تفعيل البحث في الجدول
    document.addEventListener('DOMContentLoaded', function() {
        // حذف العضو عبر AJAX
        document.querySelectorAll('.btn-delete-member').forEach(function(btn) {
            btn.addEventListener('click', function() {
                var memberId = this.getAttribute('data-member-id');
                var memberName = this.getAttribute('data-member-name');
                if (confirm('هل أنت متأكد من حذف العضو "' + memberName + '"؟')) {
                    fetch('{% url 'member_delete' 0 %}'.replace('0', memberId), {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': '{{ csrf_token }}',
                            'X-Requested-With': 'XMLHttpRequest',
                        },
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert(data.error || 'حدث خطأ أثناء الحذف');
                        }
                    });
                }
            });
        });
    });
</script>
{% endblock %}
