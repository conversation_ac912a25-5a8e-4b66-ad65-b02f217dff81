{% extends 'base.html' %}
{% load static %}

{% block title %}تقرير حالة الأعضاء{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="page-title mb-4">
            <i class="fas fa-user-check me-3"></i>
            تقرير حالة الأعضاء
        </h1>
        
        <!-- أزرار التصدير -->
        <div class="mb-4">
            <a href="{% url 'member_status_export_excel' %}" class="btn btn-success">
                <i class="fas fa-file-excel me-2"></i>تصدير Excel
            </a>
            <a href="{% url 'member_status_export_pdf' %}" class="btn btn-danger">
                <i class="fas fa-file-pdf me-2"></i>تصدير PDF
            </a>
            <a href="{% url 'reports_dashboard' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
            </a>
        </div>
        
        <!-- إحصائيات سريعة -->
        {% if stats %}
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h3>{{ stats.total_members }}</h3>
                        <p class="mb-0">إجمالي الأعضاء</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h3>{{ stats.regular_members }}</h3>
                        <p class="mb-0">أعضاء منتظمين</p>
                        <small>({{ stats.regular_percentage|floatformat:1 }}%)</small>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h3>{{ stats.warning_members }}</h3>
                        <p class="mb-0">تحذير</p>
                        <small>({{ stats.warning_percentage|floatformat:1 }}%)</small>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <h3>{{ stats.late_members }}</h3>
                        <p class="mb-0">متأخرين</p>
                        <small>({{ stats.late_percentage|floatformat:1 }}%)</small>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- جدول حالة الأعضاء -->
        <div class="card">
            <div class="card-body">
                {% if report_data %}
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>اسم العضو</th>
                                <th>رقم الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>الحالة</th>
                                <th>آخر دفعة</th>
                                <th>مبلغ آخر دفعة</th>
                                <th>إجمالي الدفعات</th>
                                <th>أيام منذ آخر دفعة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for data in report_data %}
                            <tr>
                                <td>
                                    <strong>{{ data.member.name }}</strong>
                                </td>
                                <td>{{ data.member.phone|default:"-" }}</td>
                                <td>{{ data.member.email|default:"-" }}</td>
                                <td>
                                    <span class="badge bg-{{ data.status_class }}">
                                        {{ data.status }}
                                    </span>
                                </td>
                                <td>
                                    {% if data.last_payment %}
                                        {{ data.last_payment.payment_date }}
                                    {% else %}
                                        <span class="text-muted">لا توجد دفعات</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if data.last_payment %}
                                        {{ data.last_payment.amount|floatformat:2 }} ₪
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ data.total_payments|floatformat:2 }} ₪</strong>
                                </td>
                                <td>
                                    {% if data.days_since_payment > 0 %}
                                        <span class="badge bg-{{ data.status_class }}">
                                            {{ data.days_since_payment }} يوم
                                        </span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد بيانات أعضاء لعرضها.
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- ملاحظات -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            ملاحظات حول التقرير
                        </h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2">
                                <span class="badge bg-success me-2">منتظم</span>
                                العضو دفع خلال آخر 30 يوم
                            </li>
                            <li class="mb-2">
                                <span class="badge bg-warning me-2">تحذير</span>
                                العضو لم يدفع منذ 30-45 يوم
                            </li>
                            <li class="mb-2">
                                <span class="badge bg-danger me-2">متأخر</span>
                                العضو لم يدفع منذ أكثر من 45 يوم أو لم يدفع مطلقاً
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
